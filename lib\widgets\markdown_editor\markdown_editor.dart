import 'package:flutter/material.dart';
import 'package:novel_app/widgets/markdown_editor/editor_field.dart';
import 'package:novel_app/widgets/markdown_editor/editor_icons.dart';
import 'package:novel_app/widgets/markdown_editor/editor_preview.dart';

/// 一个封装了markdown编辑所需组件的编辑器类
/// 
/// [field] 是 [MarkdownEditingField] 编辑区域组件
/// [icons] 是 [MarkdownEditorIcons] 工具栏组件  
/// [preview] 是 [MarkdownPreview] 预览组件
/// [vertical] 是垂直布局的完整编辑器
/// [inPlace] 是可切换编辑/预览模式的编辑器
class MarkdownEditor {
  /// 创建一个 [MarkdownEditor] 实例
  /// 
  /// [controller] 文本控制器，如果为null会自动创建
  /// [selectable] 预览区域是否可选择文本，默认为true
  /// [maxLines] 编辑区域最大行数，默认为null（无限制）
  MarkdownEditor({
    TextEditingController? controller,
    this.selectable = true,
    this.maxLines,
  }) : _controller = controller ?? TextEditingController() {
    _field = MarkdownEditingField(
      controller: _controller,
      key: UniqueKey(),
      onChange: _setState,
      maxLines: maxLines,
    );
    
    _icons = MarkdownEditorIcons(
      controller: _controller,
      key: UniqueKey(),
      afterEditing: _setState,
    );
    
    _preview = StatefulBuilder(
      builder: (context, setState) {
        _setStateFunction = setState;
        return MarkdownPreview(
          text: _controller.text,
          selectable: selectable,
          key: UniqueKey(),
        );
      },
    );
  }

  final TextEditingController _controller;
  late Widget _field;
  late Widget _icons;
  late Widget _preview;
  late Function _setStateFunction;

  /// 预览区域是否可选择文本
  final bool selectable;
  
  /// 编辑区域最大行数
  final int? maxLines;

  /// 获取编辑区域组件
  Widget get field => _field;

  /// 获取工具栏组件
  Widget get icons => _icons;

  /// 获取预览组件
  Widget get preview => _preview;

  /// 获取文本控制器
  TextEditingController get controller => _controller;

  /// 垂直布局的完整编辑器
  /// 包含编辑区域、工具栏和预览区域
  Widget vertical() {
    return Column(
      children: [
        field,
        icons,
        Flexible(
          child: preview,
        ),
      ],
    );
  }

  /// 可切换编辑/预览模式的编辑器
  /// 通过开关在编辑模式和预览模式之间切换
  Widget inPlace() {
    bool isPreviewing = false;
    return StatefulBuilder(builder: (context, setState) {
      return Column(
        children: [
          Row(
            children: [
              const Text('编辑'),
              Switch(
                value: isPreviewing,
                onChanged: (value) {
                  setState(() {
                    isPreviewing = value;
                  });
                },
              ),
              const Text('预览'),
            ],
          ),
          Expanded(
            child: Stack(
              children: [
                if (!isPreviewing)
                  Column(
                    children: [
                      Expanded(child: field),
                      icons,
                    ],
                  ),
                if (isPreviewing) preview,
              ],
            ),
          ),
        ],
      );
    });
  }

  /// 分屏模式编辑器
  /// 左侧编辑，右侧预览
  Widget splitView() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              Expanded(child: field),
              icons,
            ],
          ),
        ),
        const VerticalDivider(width: 1),
        Expanded(
          child: preview,
        ),
      ],
    );
  }

  void _setState() {
    _setStateFunction(() {});
  }

  /// 释放资源
  void dispose() {
    _controller.dispose();
  }
}
