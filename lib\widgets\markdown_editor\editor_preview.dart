import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:url_launcher/url_launcher.dart';

/// Markdown预览组件
/// 
/// 使用 [flutter_markdown] 包中的 [Markdown] 组件来预览传入的markdown [text]
/// [selectable] 用于确定预览内容是否可选择，默认为true
class MarkdownPreview extends StatelessWidget {
  /// 创建 [MarkdownPreview]
  /// 
  /// [text] 要显示的markdown文本
  /// [selectable] 是否可选择文本
  /// [styleSheet] 自定义样式表
  /// [onTapLink] 链接点击回调
  const MarkdownPreview({
    Key? key,
    required this.text,
    this.selectable = true,
    this.styleSheet,
    this.onTapLink,
  }) : super(key: key);

  /// 要由 [Markdown] 组件显示的文本
  final String text;

  /// 是否可选择文本
  final bool selectable;

  /// 自定义样式表
  final MarkdownStyleSheet? styleSheet;

  /// 链接点击回调
  final MarkdownTapLinkCallback? onTapLink;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Scrollbar(
        child: Markdown(
          data: text.isEmpty ? '# 预览\n\n在左侧编辑区域输入Markdown文本，这里会实时显示预览效果。' : text,
          selectable: selectable,
          styleSheet: styleSheet ?? _getDefaultStyleSheet(context),
          onTapLink: onTapLink ?? _defaultOnTapLink,
          extensionSet: md.ExtensionSet(
            md.ExtensionSet.gitHubFlavored.blockSyntaxes,
            [
              md.EmojiSyntax(),
              ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes,
            ],
          ),
          shrinkWrap: true,
          padding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  MarkdownStyleSheet _getDefaultStyleSheet(BuildContext context) {
    final theme = Theme.of(context);
    return MarkdownStyleSheet(
      p: theme.textTheme.bodyMedium?.copyWith(
        fontSize: 16,
        height: 1.6,
      ),
      h1: theme.textTheme.headlineLarge?.copyWith(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: theme.primaryColor,
      ),
      h2: theme.textTheme.headlineMedium?.copyWith(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: theme.primaryColor,
      ),
      h3: theme.textTheme.headlineSmall?.copyWith(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: theme.primaryColor,
      ),
      h4: theme.textTheme.titleLarge?.copyWith(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
      h5: theme.textTheme.titleMedium?.copyWith(
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
      h6: theme.textTheme.titleSmall?.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
      code: TextStyle(
        fontFamily: 'monospace',
        fontSize: 14,
        backgroundColor: Colors.grey.shade100,
        color: Colors.red.shade700,
      ),
      codeblockDecoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      blockquote: theme.textTheme.bodyMedium?.copyWith(
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade600,
      ),
      blockquoteDecoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          left: BorderSide(
            color: Colors.grey.shade400,
            width: 4,
          ),
        ),
      ),
      listBullet: theme.textTheme.bodyMedium?.copyWith(
        fontWeight: FontWeight.bold,
      ),
      tableHead: theme.textTheme.bodyMedium?.copyWith(
        fontWeight: FontWeight.bold,
      ),
      tableBody: theme.textTheme.bodyMedium,
      tableBorder: TableBorder.all(
        color: Colors.grey.shade300,
        width: 1,
      ),
      tableHeadAlign: TextAlign.center,
      tableCellsPadding: const EdgeInsets.all(8),
      a: TextStyle(
        color: theme.primaryColor,
        decoration: TextDecoration.underline,
      ),
      em: const TextStyle(fontStyle: FontStyle.italic),
      strong: const TextStyle(fontWeight: FontWeight.bold),
      del: const TextStyle(decoration: TextDecoration.lineThrough),
    );
  }

  void _defaultOnTapLink(String text, String? href, String title) async {
    if (href == null) return;

    try {
      final uri = Uri.parse(href);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        debugPrint('无法打开链接: $href');
      }
    } catch (e) {
      debugPrint('打开链接时出错: $e');
    }
  }
}
