// 调试暂停状态问题的辅助脚本
// 这个文件可以帮助开发者理解和测试暂停状态的修复

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/novel.dart';

class PauseStatusDebugger {
  static void debugNovelStatus(Novel novel) {
    final controller = Get.find<NovelController>();
    
    print('=== 暂停状态调试 ===');
    print('小说标题: ${novel.title}');
    print('SessionId: ${novel.sessionId}');
    print('章节数量: ${novel.chapters.length}');
    
    // 检查实时状态
    print('\n--- 实时状态 ---');
    print('当前SessionId: ${controller.currentSessionId}');
    print('isPaused: ${controller.isPaused.value}');
    print('isGenerating: ${controller.isGenerating.value}');
    
    // 检查保存的状态
    print('\n--- 保存状态 ---');
    if (novel.sessionId != null && novel.sessionId!.isNotEmpty) {
      final savedState = controller._getGenerationStateSync(novel.sessionId!);
      if (savedState != null) {
        print('保存的状态存在');
        print('isPaused: ${savedState['isPaused']}');
        print('isGenerating: ${savedState['isGenerating']}');
        print('targetTotalChapters: ${savedState['targetTotalChapters']}');
        print('completedChapters: ${savedState['completedChapters']}');
      } else {
        print('没有找到保存的状态');
      }
    } else {
      print('SessionId为空');
    }
    
    // 检查生成完成状态
    print('\n--- 完成状态检查 ---');
    final isComplete = controller.isNovelGenerationComplete(novel);
    print('isNovelGenerationComplete: $isComplete');
    
    // 检查最终状态
    print('\n--- 最终状态 ---');
    final status = controller.getNovelGenerationStatus(novel);
    print('最终状态: $status');
    
    print('=== 调试结束 ===\n');
  }
  
  static Widget buildDebugButton(Novel novel) {
    return IconButton(
      icon: const Icon(Icons.bug_report, color: Colors.orange),
      onPressed: () {
        debugNovelStatus(novel);
        Get.snackbar(
          '调试完成',
          '已在控制台输出 ${novel.title} 的详细状态信息',
          duration: const Duration(seconds: 2),
        );
      },
      tooltip: '调试状态',
    );
  }
  
  static void testPauseStatusFix() {
    print('=== 暂停状态修复测试 ===');
    print('1. 开始生成一部小说');
    print('2. 在生成过程中点击暂停');
    print('3. 检查书库中的状态显示');
    print('4. 预期结果：状态应为"已暂停"而不是"已完成"');
    print('========================');
  }
}

// 扩展NovelController以暴露私有方法用于调试
extension NovelControllerDebug on NovelController {
  Map<String, dynamic>? getGenerationStateSync(String sessionId) {
    return _getGenerationStateSync(sessionId);
  }
}
