﻿import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../controllers/novel_agent_controller.dart';
import '../controllers/novel_controller.dart';
import '../models/novel.dart';
import '../widgets/novel_agent_panel.dart';
import '../services/ai_service.dart';
import '../widgets/markdown_editor/markdown_editor.dart';

class DaizongAIAssistantScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const DaizongAIAssistantScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<DaizongAIAssistantScreen> createState() =>
      _DaizongAIAssistantScreenState();
}

class _DaizongAIAssistantScreenState extends State<DaizongAIAssistantScreen> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  final NovelController _novelController = Get.find<NovelController>();
  final AIService _aiService = Get.find<AIService>();

  final List<TextEditingController> _chapterControllers = [];
  final List<TextEditingController> _chapterTitleControllers = [];
  final List<MarkdownEditor> _markdownEditors = [];
  final ScrollController _editorScrollController = ScrollController();

  late Novel _currentNovel;
  late Novel _originalNovel; // 保存原始状态用于撤销
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  double _aiPanelWidth = 400;

  // 响应式设计相关
  bool get _isMobile =>
      !kIsWeb &&
          (defaultTargetPlatform == TargetPlatform.android ||
              defaultTargetPlatform == TargetPlatform.iOS) ||
      MediaQuery.of(context).size.width < 600;
  bool get _isTablet =>
      MediaQuery.of(context).size.width >= 600 &&
      MediaQuery.of(context).size.width < 1024;
  bool get _isDesktop => MediaQuery.of(context).size.width >= 1024;

  // 手机端的Tab控制
  int _currentTabIndex = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel;
    _originalNovel = _createNovelCopy(widget.novel); // 保存原始状态
    _currentChapterIndex = widget.initialChapterIndex ?? 0;

    // 使用 addPostFrameCallback 确保在安全的时机初始化控制器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initChapterControllers();
      _initAgent();

      // 只在没有第0章时才创建（不强制创建）
      if (_getMasterChapterIndex() == -1 && _currentNovel.chapters.isNotEmpty) {
        print('[初始化] 检测到没有第0章，但有其他章节，可考虑创建总细纲');
        // 这里不自动创建，让用户决定是否需要
      }
    });
  }

  void _initChapterControllers() {
    try {
      // 清空现有控制器，先移除监听器再释放
      for (final controller in _chapterControllers) {
        controller.removeListener(_onTextChanged);
        controller.dispose();
      }
      for (final controller in _chapterTitleControllers) {
        controller.removeListener(_onTitleChanged);
        controller.dispose();
      }

      _chapterControllers.clear();
      _chapterTitleControllers.clear();
      _markdownEditors.clear();

      print('初始化控制器，章节数量: ${_currentNovel.chapters.length}');

      for (int i = 0; i < _currentNovel.chapters.length; i++) {
        final chapter = _currentNovel.chapters[i];

        // 内容控制器
        final contentController = TextEditingController(text: chapter.content);
        contentController.addListener(_onTextChanged);
        _chapterControllers.add(contentController);

        // 标题控制器
        final titleController = TextEditingController(text: chapter.title);
        titleController.addListener(_onTitleChanged);
        _chapterTitleControllers.add(titleController);

        // Markdown编辑器
        final markdownEditor = MarkdownEditor(
          controller: contentController,
          selectable: true,
        );
        _markdownEditors.add(markdownEditor);

        print('初始化第${i}章控制器: ${chapter.title}');
      }

      print(
          '控制器初始化完成 - 内容控制器: ${_chapterControllers.length}, 标题控制器: ${_chapterTitleControllers.length}');

      // 确保当前章节索引有效
      if (_currentChapterIndex >= _currentNovel.chapters.length) {
        _currentChapterIndex = _currentNovel.chapters.length - 1;
      }
      if (_currentChapterIndex < 0 && _currentNovel.chapters.isNotEmpty) {
        _currentChapterIndex = 0;
      }
    } catch (e) {
      print('初始化控制器时出错: $e');
    }
  }

  /// 验证控制器状态是否有效
  bool _isControllerStateValid() {
    return _currentChapterIndex >= 0 &&
        _currentChapterIndex < _currentNovel.chapters.length &&
        _currentChapterIndex < _chapterControllers.length &&
        _currentChapterIndex < _chapterTitleControllers.length;
  }

  /// 确保控制器和章节数据的一致性（安全版本，不触发重建）
  void _ensureControllerConsistency() {
    // 检查控制器数量是否与章节数量匹配
    if (_chapterControllers.length != _currentNovel.chapters.length ||
        _chapterTitleControllers.length != _currentNovel.chapters.length) {
      print(
          '控制器数量不匹配，需要重新初始化: 章节=${_currentNovel.chapters.length}, 内容控制器=${_chapterControllers.length}, 标题控制器=${_chapterTitleControllers.length}');
      // 使用 addPostFrameCallback 在安全的时机重新初始化
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initChapterControllers();
      });
    }

    // 确保当前章节索引有效
    if (_currentChapterIndex >= _currentNovel.chapters.length) {
      _currentChapterIndex = _currentNovel.chapters.length - 1;
    }
    if (_currentChapterIndex < 0 && _currentNovel.chapters.isNotEmpty) {
      _currentChapterIndex = 0;
    }
  }

  void _initAgent() async {
    await _agentController.setCurrentNovel(_currentNovel);

    if (_currentNovel.chapters.isNotEmpty &&
        _currentChapterIndex < _currentNovel.chapters.length) {
      _agentController.currentChapter.value =
          _currentNovel.chapters[_currentChapterIndex];
    }

    // 监听章节内容变化
    _agentController.currentChapter.listen((chapter) {
      if (chapter != null && _isControllerStateValid()) {
        try {
          // 更新编辑器内容，但不触发 _onTextChanged
          final controller = _chapterControllers[_currentChapterIndex];
          controller.removeListener(_onTextChanged);
          controller.text = chapter.content;
          controller.addListener(_onTextChanged);
        } catch (e) {
          print('更新章节内容时出错: $e');
        }
      }
    });
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }

    // 延迟同步第0章（避免频繁更新）
    _scheduleOutlineSync();
  }

  Timer? _syncTimer;

  /// 安排延迟同步第0章
  void _scheduleOutlineSync() {
    // 取消之前的定时器
    _syncTimer?.cancel();

    // 设置新的延迟同步（2秒后执行）
    _syncTimer = Timer(const Duration(seconds: 2), () {
      _performOutlineSync();
    });
  }

  /// 执行同步操作
  void _performOutlineSync() async {
    try {
      // 检查当前章节是否为正常章节（非第0章）
      if (_currentChapterIndex >= 0 &&
          _currentChapterIndex < _currentNovel.chapters.length) {
        final currentChapter = _currentNovel.chapters[_currentChapterIndex];

        // 只有当前编辑的是正常章节时才同步
        if (currentChapter.number > 0) {
          // 先更新当前章节的内容
          if (_currentChapterIndex < _chapterControllers.length) {
            final controller = _chapterControllers[_currentChapterIndex];
            _currentNovel.chapters[_currentChapterIndex] = currentChapter.copyWith(
              content: controller.text,
            );
          }

          // 然后同步第0章
          await _updateMasterChapterOutline();

          // 确保在主线程中更新UI
          if (mounted) {
            setState(() {
              // 强制刷新UI以显示第0章的更新
            });
          }
        }
      }
    } catch (e) {
      print('[同步] 延迟同步失败: $e');
    }
  }

  void _onTitleChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  /// 创建小说的深度副本
  Novel _createNovelCopy(Novel novel) {
    return novel.copyWith(
      chapters: novel.chapters.map((chapter) => chapter.copyWith()).toList(),
    );
  }

  /// 撤销所有修改
  void _undoChanges() {
    if (!_hasChanges) return;

    // 恢复到原始状态
    _currentNovel = _createNovelCopy(_originalNovel);

    // 重新初始化控制器
    _initChapterControllers();

    // 同步Agent控制器状态
    _agentController.setCurrentNovel(_currentNovel);
    if (_currentNovel.chapters.isNotEmpty &&
        _currentChapterIndex < _currentNovel.chapters.length) {
      _agentController.currentChapter.value =
          _currentNovel.chapters[_currentChapterIndex];
    }

    // 重置状态
    setState(() {
      _hasChanges = false;
    });

    Get.snackbar(
      '撤销成功',
      '已恢复到上次保存的状态',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// 取消修改并退出
  void _cancelChanges() {
    if (_hasChanges) {
      Get.dialog(
        AlertDialog(
          title: const Text('确认取消'),
          content: const Text('您有未保存的修改，确定要放弃这些修改并退出吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('继续编辑'),
            ),
            TextButton(
              onPressed: () {
                Get.back(); // 关闭对话框
                Get.back(); // 退出页面
              },
              child: const Text('放弃修改'),
            ),
          ],
        ),
      );
    } else {
      Get.back(); // 没有修改直接退出
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: _isMobile
            ? _buildMobileLayout(context)
            : _buildDesktopLayout(context),
      ),
      bottomNavigationBar: _isMobile ? _buildMobileBottomNavigation() : null,
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(_isMobile ? 56 : 48),
      child: AppBar(
        title: Text(
          _isMobile
              ? '《${_currentNovel.title}》'
              : '《${_currentNovel.title}》- 岱宗AI辅助助手',
          style: TextStyle(fontSize: _isMobile ? 18 : 16),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: Icon(Icons.close, size: _isMobile ? 24 : 20),
          onPressed: _cancelChanges,
          tooltip: '取消修改',
        ),
        actions: [
          // Markdown渲染按钮
          Obx(() => IconButton(
                icon: Icon(
                  _agentController.isMarkdownMode.value
                      ? Icons.text_fields
                      : Icons.code,
                  size: _isMobile ? 24 : 20,
                ),
                onPressed: () {
                  _agentController.isMarkdownMode.value =
                      !_agentController.isMarkdownMode.value;
                },
                tooltip: _agentController.isMarkdownMode.value
                    ? '普通文本'
                    : 'Markdown渲染',
              )),
          // 工具广场按钮
          IconButton(
            icon: Icon(Icons.build, size: _isMobile ? 24 : 20),
            onPressed: () => Get.toNamed('/tools'),
            tooltip: '工具广场',
          ),
          // 章节管理按钮
          IconButton(
            icon: Icon(Icons.library_books, size: _isMobile ? 24 : 20),
            onPressed: () {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showChapterManagementDialog();
              });
            },
            tooltip: '章节管理',
          ),
          // 撤销按钮（只在有修改时显示）
          if (_hasChanges)
            IconButton(
              icon: Icon(Icons.undo, size: _isMobile ? 24 : 20),
              onPressed: _undoChanges,
              tooltip: '撤销修改',
            ),
          // 保存按钮
          IconButton(
            icon: Icon(Icons.save, size: _isMobile ? 24 : 20),
            onPressed: _hasChanges ? _saveChanges : null,
            tooltip: '保存',
          ),
          if (!_isMobile && _isTablet) // 平板端显示切换按钮
            IconButton(
              icon: const Icon(Icons.swap_horiz),
              onPressed: () {
                setState(() {
                  _currentTabIndex = _currentTabIndex == 0 ? 1 : 0;
                });
              },
              tooltip: '切换视图',
            ),
        ],
      ),
    );
  }

  /// 构建手机端布局
  Widget _buildMobileLayout(BuildContext context) {
    return PageView(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentTabIndex = index;
        });
      },
      children: [
        _buildEditor(),
        Container(
          padding: const EdgeInsets.all(8),
          child: NovelAgentPanel(
            novel: _currentNovel,
            width: MediaQuery.of(context).size.width - 16,
            isMobile: true,
          ),
        ),
      ],
    );
  }

  /// 构建桌面端布局
  Widget _buildDesktopLayout(BuildContext context) {
    if (_isTablet) {
      // 平板端使用可切换的单面板布局
      return _currentTabIndex == 0
          ? _buildEditor()
          : Container(
              padding: const EdgeInsets.all(8),
              child: NovelAgentPanel(
                novel: _currentNovel,
                width: MediaQuery.of(context).size.width - 16,
                isMobile: false,
              ),
            );
    } else {
      // 桌面端使用传统的双面板布局
      return Row(
        children: [
          // 左侧编辑器区域
          Expanded(
            child: _buildEditor(),
          ),

          // 可拖拽的分隔条
          _buildResizableHandle(context),

          // 右侧AI面板区域
          Container(
            width: _aiPanelWidth,
            child: NovelAgentPanel(
              novel: _currentNovel,
              width: _aiPanelWidth,
              isMobile: false,
            ),
          ),
        ],
      );
    }
  }

  /// 构建手机端底部导航
  Widget _buildMobileBottomNavigation() {
    return BottomNavigationBar(
      currentIndex: _currentTabIndex,
      onTap: (index) {
        setState(() {
          _currentTabIndex = index;
        });
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.edit),
          label: '编辑器',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.auto_fix_high),
          label: 'AI助手',
        ),
      ],
    );
  }

  /// 构建可拖拽的分隔条
  Widget _buildResizableHandle(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.resizeColumn,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _aiPanelWidth =
                (_aiPanelWidth - details.delta.dx).clamp(300.0, 600.0);
          });
        },
        child: Container(
          width: 8,
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 2,
              color: Theme.of(context).dividerColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: _isMobile ? 40 : 36,
      padding: EdgeInsets.symmetric(horizontal: _isMobile ? 8 : 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '章节：',
            style: TextStyle(
              fontSize: _isMobile ? 12 : 13,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: _isMobile ? 4 : 6),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // 现有章节
                  ..._currentNovel.chapters.asMap().entries.map((entry) {
                    final index = entry.key;
                    final chapter = entry.value;
                    final isSelected = index == _currentChapterIndex;

                    // 调试信息（仅在开发模式下输出）
                    if (kDebugMode && false) {
                      // 暂时关闭调试输出
                      print(
                          '章节调试: index=$index, number=${chapter.number}, title=${chapter.title}');
                    }

                    return Padding(
                      padding: EdgeInsets.only(right: _isMobile ? 4 : 6),
                      child: GestureDetector(
                        onLongPress: () {
                          // 使用 WidgetsBinding.instance.addPostFrameCallback 来避免在构建过程中调用
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _showChapterMenu(index);
                          });
                        },
                        onSecondaryTap: () {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _showChapterMenu(index);
                          });
                        },
                        child: ChoiceChip(
                          label: Text(
                            _getChapterLabel(chapter),
                            style: TextStyle(fontSize: _isMobile ? 11 : 12),
                          ),
                          selected: isSelected,
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity: VisualDensity.compact,
                          onSelected: (selected) {
                            if (selected) {
                              _switchToChapter(index);
                            }
                          },
                        ),
                      ),
                    );
                  }).toList(),
                  // 添加新章节按钮
                  Padding(
                    padding: EdgeInsets.only(right: _isMobile ? 4 : 6),
                    child: ActionChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.add,
                            size: _isMobile ? 14 : 16,
                            color: Theme.of(context).primaryColor,
                          ),
                          SizedBox(width: _isMobile ? 2 : 4),
                          Text(
                            '新章节',
                            style: TextStyle(
                              fontSize: _isMobile ? 11 : 12,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                      onPressed: () {
                        // 使用 WidgetsBinding.instance.addPostFrameCallback 来避免在构建过程中调用
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _showCreateChapterDialog();
                        });
                      },
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                      backgroundColor:
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      side: BorderSide(
                        color: Theme.of(context)
                            .primaryColor
                            .withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_books,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              '暂无章节内容',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '点击上方的"新章节"按钮开始创作',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showCreateChapterDialog,
              icon: const Icon(Icons.add),
              label: const Text('创建第一章'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    if (_currentChapterIndex >= _chapterControllers.length ||
        _currentChapterIndex >= _chapterTitleControllers.length) {
      return const Center(
        child: Text('章节索引超出范围'),
      );
    }

    // 添加调试信息（仅在开发模式下输出）
    if (kDebugMode && false) {
      // 暂时关闭调试输出
      print('构建编辑器内容 - 当前章节索引: $_currentChapterIndex');
      print('章节数量: ${_currentNovel.chapters.length}');
      print('内容控制器数量: ${_chapterControllers.length}');
      print('标题控制器数量: ${_chapterTitleControllers.length}');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 可编辑的章节标题
        Container(
          height: _isMobile ? 36 : 40, // 移动端更紧凑
          child: Row(
            children: [
              Text(
                _getChapterPrefix(_currentNovel.chapters[_currentChapterIndex]),
                style: TextStyle(
                  fontSize: _isMobile ? 14 : 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              Expanded(
                child: _currentChapterIndex < _chapterTitleControllers.length &&
                        _currentChapterIndex >= 0 &&
                        _currentChapterIndex < _currentNovel.chapters.length
                    ? TextField(
                        controller:
                            _chapterTitleControllers[_currentChapterIndex],
                        style: TextStyle(
                          fontSize: _isMobile ? 14 : 16,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: '章节标题',
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: _isMobile ? 6 : 8,
                              vertical: _isMobile ? 6 : 8),
                          isDense: true,
                        ),
                        maxLines: 1,
                      )
                    : _currentChapterIndex >= 0 &&
                            _currentChapterIndex < _currentNovel.chapters.length
                        ? Text(
                            _currentNovel.chapters[_currentChapterIndex].title,
                            style: TextStyle(
                              fontSize: _isMobile ? 14 : 16,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          )
                        : Text(
                            '无效章节',
                            style: TextStyle(
                              fontSize: _isMobile ? 14 : 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
              ),
            ],
          ),
        ),
        SizedBox(height: _isMobile ? 6 : 8), // 移动端减少间距
        Expanded(
          child: _currentChapterIndex < _chapterControllers.length &&
                  _currentChapterIndex >= 0 &&
                  _currentChapterIndex < _currentNovel.chapters.length
              ? Obx(() => _agentController.isMarkdownMode.value
                  ? _buildMarkdownView()
                  : _buildMarkdownEditor())
              : Container(
                  decoration: const BoxDecoration(
                    border:
                        Border.fromBorderSide(BorderSide(color: Colors.grey)),
                  ),
                  padding: EdgeInsets.all(_isMobile ? 12 : 16),
                  child: const Center(
                    child: Text(
                      '请选择一个章节进行编辑',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  void _switchToChapter(int index) {
    if (index >= 0 && index < _currentNovel.chapters.length) {
      // 确保控制器状态一致
      _ensureControllerConsistency();

      // 如果切换到第0章，先刷新其内容
      bool isRefreshed = false;
      if (_currentNovel.chapters[index].number == 0) {
        _refreshMasterChapterContent(index);
        isRefreshed = true;
      }

      setState(() {
        _currentChapterIndex = index;
      });

      // 更新 Agent 控制器的当前章节
      _agentController.currentChapter.value = _currentNovel.chapters[index];

      // 同步编辑器内容和标题，添加安全检查
      // 注意：如果第0章已经刷新过，控制器已经在刷新方法中更新了
      try {
        if (!isRefreshed) {
          // 只有非第0章或未刷新的章节才需要同步控制器
          if (index < _chapterControllers.length &&
              index < _currentNovel.chapters.length) {
            final contentController = _chapterControllers[index];
            contentController.removeListener(_onTextChanged);
            contentController.text = _currentNovel.chapters[index].content;
            contentController.addListener(_onTextChanged);
          }

          // 同步markdown编辑器
          if (index < _markdownEditors.length) {
            _markdownEditors[index].controller.removeListener(_onTextChanged);
            _markdownEditors[index].controller.text = _currentNovel.chapters[index].content;
            _markdownEditors[index].controller.addListener(_onTextChanged);
          }
        }

        // 标题控制器总是需要同步
        if (index < _chapterTitleControllers.length &&
            index < _currentNovel.chapters.length) {
          final titleController = _chapterTitleControllers[index];
          titleController.removeListener(_onTitleChanged);
          titleController.text = _currentNovel.chapters[index].title;
          titleController.addListener(_onTitleChanged);
        }
      } catch (e) {
        print('切换章节时同步内容出错: $e');
        // 如果同步失败，重新初始化控制器
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _initChapterControllers();
        });
      }
    }
  }

  void _saveChanges() async {
    try {
      // 保存章节内容和标题
      for (int i = 0; i < _chapterControllers.length; i++) {
        if (i < _currentNovel.chapters.length) {
          // 更新内容
          _currentNovel.chapters[i].content = _chapterControllers[i].text;

          // 更新标题（使用copyWith创建新实例）
          if (i < _chapterTitleControllers.length) {
            final newTitle = _chapterTitleControllers[i].text;
            if (newTitle != _currentNovel.chapters[i].title) {
              _currentNovel.chapters[i] = _currentNovel.chapters[i].copyWith(
                title: newTitle,
                content: _chapterControllers[i].text,
              );
            }
          }
        }
      }

      // 同步更新第0章的总细纲
      await _updateMasterChapterOutline();

      await _novelController.updateNovel(_currentNovel);

      // 保存成功后更新原始状态
      _originalNovel = _createNovelCopy(_currentNovel);

      setState(() {
        _hasChanges = false;
      });

      Get.snackbar(
        '保存成功',
        '章节内容已保存并同步细纲',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存章节内容时出错: $e',
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red[700],
      );
    }
  }





  /// 生成基础细纲模板
  String _generateBasicOutlineTemplate(Chapter chapter) {
    if (chapter.content.trim().isEmpty) {
      return '本章节暂无内容';
    }

    // 简单的内容概要生成
    final content = chapter.content.trim();
    if (content.length <= 200) {
      return content;
    } else {
      // 取前200个字符作为概要
      return content.substring(0, 200) + '...';
    }
  }

  /// 确保第0章存在
  void _ensureMasterChapterExists() {
    // 检查是否已存在第0章
    final masterIndex = _getMasterChapterIndex();
    if (masterIndex != -1) {
      print('[同步] 第0章已存在，无需创建');
      return;
    }

    // 创建第0章
    final masterChapter = Chapter(
      number: 0,
      title: '总细纲',
      content: _generateInitialMasterContent(),
    );

    // 插入到开头
    _currentNovel.chapters.insert(0, masterChapter);

    // 为新的第0章创建控制器
    final newContentController = TextEditingController(text: masterChapter.content);
    final newTitleController = TextEditingController(text: masterChapter.title);

    // 添加监听器
    newContentController.addListener(_onTextChanged);
    newTitleController.addListener(_onTitleChanged);

    // 插入到控制器列表的开头
    _chapterControllers.insert(0, newContentController);
    _chapterTitleControllers.insert(0, newTitleController);

    // 创建markdown编辑器
    final newMarkdownEditor = MarkdownEditor(
      controller: newContentController,
    );
    _markdownEditors.insert(0, newMarkdownEditor);

    // 更新当前章节索引（因为插入了新章节）
    if (_currentChapterIndex >= 0) {
      _currentChapterIndex++;
    }

    print('[同步] 已创建第0章总细纲');
  }

  /// 生成初始的第0章内容
  String _generateInitialMasterContent() {
    return '''# ${_currentNovel.title} - 总细纲

## 小说信息
- **类型：** ${_currentNovel.genre}
- **创建时间：** ${_formatDateTime(DateTime.now())}
- **状态：** 创作中

## 总体大纲
${_currentNovel.outline.isNotEmpty ? _currentNovel.outline : '待完善总体大纲...'}

## 章节细纲
（此部分将自动同步各章节的细纲信息）

---
*注意：此页面为自动生成的总细纲，会根据各章节内容自动更新*''';
  }

  /// 更新第0章总细纲
  Future<void> _updateMasterChapterOutline() async {
    try {
      // 检查第0章是否已存在
      final masterIndex = _getMasterChapterIndex();

      if (masterIndex == -1) {
        // 第0章不存在，创建一个新的
        _ensureMasterChapterExists();
        print('[同步] 创建了新的第0章总细纲');
      } else {
        // 第0章已存在，不进行自动同步更新
        print('[同步] 第0章已存在，跳过自动同步');
      }
    } catch (e) {
      print('[同步] 更新第0章总细纲失败: $e');
    }
  }

  /// 刷新第0章内容（当切换到第0章时调用）
  void _refreshMasterChapterContent(int masterIndex) {
    try {
      if (_currentNovel.chapters[masterIndex].number != 0) {
        return; // 不是第0章，直接返回
      }

      // 获取所有正常章节
      final normalChapters = _currentNovel.chapters
          .where((ch) => ch.number > 0)
          .toList()
        ..sort((a, b) => a.number.compareTo(b.number));

      if (normalChapters.isEmpty) {
        print('[刷新] 没有正常章节，无需刷新第0章');
        return;
      }

      // 构建新的章节细纲部分
      final outlineSection = _buildChapterOutlineSection(normalChapters);

      // 获取当前第0章内容
      final currentContent = _currentNovel.chapters[masterIndex].content;

      // 更新章节细纲部分
      final updatedContent = _updateOutlineSectionInContent(currentContent, outlineSection);

      // 检查内容是否有变化
      if (updatedContent != currentContent) {
        // 更新第0章
        _currentNovel.chapters[masterIndex] = _currentNovel.chapters[masterIndex].copyWith(
          content: updatedContent,
        );

        // 强制更新控制器内容（这是关键！）
        if (masterIndex < _chapterControllers.length) {
          final controller = _chapterControllers[masterIndex];
          controller.removeListener(_onTextChanged);
          controller.text = updatedContent;
          controller.addListener(_onTextChanged);
        }

        // 强制更新markdown编辑器
        if (masterIndex < _markdownEditors.length) {
          _markdownEditors[masterIndex].controller.removeListener(_onTextChanged);
          _markdownEditors[masterIndex].controller.text = updatedContent;
          _markdownEditors[masterIndex].controller.addListener(_onTextChanged);
        }

        print('[刷新] 已刷新第0章内容，包含${normalChapters.length}个章节');

        // 强制触发UI重新渲染
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              // 强制刷新UI
            });
          }
        });
      } else {
        print('[刷新] 第0章内容无变化');
      }
    } catch (e) {
      print('[刷新] 刷新第0章内容失败: $e');
    }
  }

  /// 获取第0章的索引
  int _getMasterChapterIndex() {
    for (int i = 0; i < _currentNovel.chapters.length; i++) {
      if (_currentNovel.chapters[i].number == 0) {
        return i;
      }
    }
    return -1;
  }



  /// 构建章节细纲部分
  String _buildChapterOutlineSection(List<Chapter> normalChapters) {
    final buffer = StringBuffer();
    buffer.writeln('## 章节细纲');
    buffer.writeln();

    // 统计信息
    final totalWords = normalChapters.fold<int>(0, (sum, ch) => sum + ch.content.length);
    buffer.writeln('**总章节数：** ${normalChapters.length}');
    buffer.writeln('**总字数：** $totalWords');
    buffer.writeln();

    // 各章节细纲
    for (final chapter in normalChapters) {
      buffer.writeln('### 第${chapter.number}章：${chapter.title}');

      // 提取章节细纲
      final outline = _extractSimpleChapterOutline(chapter);
      buffer.writeln(outline);
      buffer.writeln();
    }

    return buffer.toString().trim();
  }

  /// 在内容中更新章节细纲部分
  String _updateOutlineSectionInContent(String currentContent, String newOutlineSection) {
    // 查找章节细纲部分的开始位置
    final outlineStartPattern = RegExp(r'^## 章节细纲\s*$', multiLine: true);
    final match = outlineStartPattern.firstMatch(currentContent);

    if (match == null) {
      // 如果没有找到章节细纲部分，直接追加
      return currentContent + '\n\n' + newOutlineSection;
    }

    // 找到章节细纲开始位置
    final startIndex = match.start;

    // 查找章节细纲部分的结束位置
    // 1. 查找下一个二级标题（## 开头但不是章节细纲）
    // 2. 查找分隔线（---）
    // 3. 如果都没找到，就到文档结尾

    final contentAfterOutline = currentContent.substring(startIndex);
    final lines = contentAfterOutline.split('\n');

    int endLineIndex = lines.length; // 默认到文档结尾

    // 从第二行开始查找（跳过"## 章节细纲"这一行）
    for (int i = 1; i < lines.length; i++) {
      final line = lines[i].trim();

      // 找到下一个二级标题（不是章节细纲相关的）
      if (line.startsWith('## ') && !line.contains('章节细纲')) {
        endLineIndex = i;
        break;
      }

      // 找到分隔线
      if (line.startsWith('---')) {
        endLineIndex = i;
        break;
      }
    }

    // 计算实际的字符位置
    final beforeOutline = currentContent.substring(0, startIndex);
    final afterOutlineLines = lines.sublist(endLineIndex);
    final afterOutline = afterOutlineLines.isNotEmpty ? '\n' + afterOutlineLines.join('\n') : '';

    // 替换章节细纲部分
    return beforeOutline + newOutlineSection + afterOutline;
  }

  /// 提取简单的章节细纲
  String _extractSimpleChapterOutline(Chapter chapter) {
    final content = chapter.content.trim();

    if (content.isEmpty) {
      return '- **状态：** 待编写\n- **字数：** 0';
    }

    // 查找明确的细纲标记
    final outlinePatterns = [
      RegExp(r'【细纲】\s*(.*?)\s*【正文】', dotAll: true),
      RegExp(r'## 细纲\s*\n(.*?)\n##', dotAll: true),
      RegExp(r'细纲：\s*\n(.*?)\n\n', dotAll: true),
    ];

    for (final pattern in outlinePatterns) {
      final match = pattern.firstMatch(content);
      if (match != null && match.group(1) != null) {
        final outline = match.group(1)!.trim();
        if (outline.isNotEmpty) {
          return '- **细纲：** $outline\n- **字数：** ${content.length}';
        }
      }
    }

    // 如果没有明确细纲，生成简要信息
    final firstLine = content.split('\n').first.trim();
    final summary = firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;

    return '- **概要：** $summary\n- **字数：** ${content.length}\n- **状态：** ${content.length > 100 ? "已完成" : "待完善"}';
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 获取章节前缀
  String _getChapterPrefix(Chapter chapter) {
    if (chapter.number == 0) {
      return '总细纲：';
    } else {
      return '第${chapter.number}章：';
    }
  }

  /// 获取章节标签
  String _getChapterLabel(Chapter chapter) {
    if (chapter.number == 0) {
      return '总细纲';
    } else {
      return '第${chapter.number}章';
    }
  }

  /// 显示创建章节对话框
  void _showCreateChapterDialog() {
    Get.dialog(
      _CreateChapterDialog(
        isMobile: _isMobile,
        onCreateChapter: _createNewChapter,
      ),
    );
  }

  /// 创建新章节
  void _createNewChapter(String title, String content) {
    try {
      // 计算新章节号（跳过第0章）
      final normalChapters = _currentNovel.chapters.where((c) => c.number > 0).toList();
      final newChapterNumber = normalChapters.isEmpty
          ? 1
          : normalChapters
                  .map((c) => c.number)
                  .reduce((a, b) => a > b ? a : b) +
              1;

      // 创建新章节
      final newChapter = Chapter(
        number: newChapterNumber,
        title: title,
        content: content.isEmpty ? '请在此编写章节内容...' : content,
      );

      // 添加到小说中
      _currentNovel.chapters.add(newChapter);

      // 创建新的控制器
      final newContentController =
          TextEditingController(text: newChapter.content);
      final newTitleController = TextEditingController(text: newChapter.title);

      // 添加监听器
      newContentController.addListener(_onTextChanged);
      newTitleController.addListener(_onTitleChanged);

      // 创建markdown编辑器
      final newMarkdownEditor = MarkdownEditor(
        controller: newContentController,
        selectable: true,
      );

      // 添加到控制器列表
      _chapterControllers.add(newContentController);
      _chapterTitleControllers.add(newTitleController);
      _markdownEditors.add(newMarkdownEditor);

      // 切换到新章节
      setState(() {
        _currentChapterIndex = _currentNovel.chapters.length - 1;
        _hasChanges = true;
      });

      // 确保控制器状态一致
      _ensureControllerConsistency();

      // 更新 Agent 控制器
      _agentController.setCurrentNovel(_currentNovel);
      _agentController.currentChapter.value = newChapter;

      Get.snackbar(
        '成功',
        '新章节"$title"已创建',
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green[700],
        icon: const Icon(Icons.check_circle, color: Colors.green),
      );
    } catch (e) {
      Get.snackbar(
        '创建失败',
        '创建章节时出错: $e',
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red[700],
      );
    }
  }

  /// 显示章节管理对话框
  void _showChapterManagementDialog() {
    Get.dialog(
      Dialog(
        child: Container(
          width: _isMobile ? double.maxFinite : 600,
          height: _isMobile ? MediaQuery.of(context).size.height * 0.8 : 500,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // 标题栏
              Row(
                children: [
                  Icon(
                    Icons.library_books,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '章节管理',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const Divider(),
              // 操作按钮
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _showCreateChapterDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('新建章节'),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: _reorderChapters,
                    icon: const Icon(Icons.reorder),
                    label: const Text('重新排序'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // 章节列表
              Expanded(
                child: ListView.builder(
                  itemCount: _currentNovel.chapters.length,
                  itemBuilder: (context, index) {
                    final chapter = _currentNovel.chapters[index];
                    final isCurrentChapter = index == _currentChapterIndex;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      color: isCurrentChapter
                          ? Theme.of(context)
                              .primaryColor
                              .withValues(alpha: 0.1)
                          : null,
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isCurrentChapter
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          child: Text(
                            '${chapter.number}',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(
                          chapter.title,
                          style: TextStyle(
                            fontWeight: isCurrentChapter
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(
                          '字数：${chapter.content.length}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isCurrentChapter)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Text(
                                  '当前',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            const SizedBox(width: 8),
                            PopupMenuButton(
                              itemBuilder: (context) => [
                                PopupMenuItem(
                                  value: 'edit',
                                  child: const Row(
                                    children: [
                                      Icon(Icons.edit, size: 16),
                                      SizedBox(width: 8),
                                      Text('编辑'),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'rename',
                                  child: const Row(
                                    children: [
                                      Icon(Icons.drive_file_rename_outline,
                                          size: 16),
                                      SizedBox(width: 8),
                                      Text('重命名'),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'duplicate',
                                  child: const Row(
                                    children: [
                                      Icon(Icons.content_copy, size: 16),
                                      SizedBox(width: 8),
                                      Text('复制'),
                                    ],
                                  ),
                                ),
                                if (_currentNovel.chapters.length > 1)
                                  PopupMenuItem(
                                    value: 'delete',
                                    child: const Row(
                                      children: [
                                        Icon(Icons.delete,
                                            size: 16, color: Colors.red),
                                        SizedBox(width: 8),
                                        Text('删除',
                                            style:
                                                TextStyle(color: Colors.red)),
                                      ],
                                    ),
                                  ),
                              ],
                              onSelected: (value) {
                                Get.back(); // 关闭管理对话框
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  switch (value) {
                                    case 'edit':
                                      _switchToChapter(index);
                                      break;
                                    case 'rename':
                                      _showRenameChapterDialog(index);
                                      break;
                                    case 'duplicate':
                                      _duplicateChapter(index);
                                      break;
                                    case 'delete':
                                      _deleteChapter(index);
                                      break;
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                        onTap: () {
                          Get.back(); // 关闭对话框
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _switchToChapter(index);
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 重新排序章节
  void _reorderChapters() {
    Get.dialog(
      Dialog(
        child: Container(
          width: _isMobile ? double.maxFinite : 500,
          height: _isMobile ? MediaQuery.of(context).size.height * 0.8 : 600,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.reorder),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '重新排序章节',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const Divider(),
              const Text(
                '拖拽章节来重新排序',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ReorderableListView.builder(
                  itemCount: _currentNovel.chapters.length,
                  onReorder: (oldIndex, newIndex) {
                    // 使用 WidgetsBinding.instance.addPostFrameCallback 来避免在构建过程中更新状态
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      setState(() {
                        if (newIndex > oldIndex) {
                          newIndex -= 1;
                        }
                        final chapter =
                            _currentNovel.chapters.removeAt(oldIndex);
                        _currentNovel.chapters.insert(newIndex, chapter);

                        // 重新编号章节
                        for (int i = 0;
                            i < _currentNovel.chapters.length;
                            i++) {
                          _currentNovel.chapters[i] =
                              _currentNovel.chapters[i].copyWith(number: i + 1);
                        }

                        // 调整当前章节索引
                        if (_currentChapterIndex == oldIndex) {
                          _currentChapterIndex = newIndex;
                        } else if (_currentChapterIndex > oldIndex &&
                            _currentChapterIndex <= newIndex) {
                          _currentChapterIndex--;
                        } else if (_currentChapterIndex < oldIndex &&
                            _currentChapterIndex >= newIndex) {
                          _currentChapterIndex++;
                        }

                        _hasChanges = true;
                      });
                    });
                  },
                  itemBuilder: (context, index) {
                    final chapter = _currentNovel.chapters[index];
                    return Card(
                      key: ValueKey(chapter.number),
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: const Icon(Icons.drag_handle),
                        title: Text('第${index + 1}章：${chapter.title}'),
                        subtitle: Text('字数：${chapter.content.length}'),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      Get.back();
                      Get.snackbar(
                        '排序完成',
                        '章节顺序已更新',
                        backgroundColor: Colors.green.withValues(alpha: 0.1),
                        colorText: Colors.green[700],
                      );
                    },
                    child: const Text('完成'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示章节菜单
  void _showChapterMenu(int index) {
    final chapter = _currentNovel.chapters[index];

    if (_isMobile) {
      // 移动端使用底部弹出菜单
      Get.bottomSheet(
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 拖拽指示器
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // 章节信息
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    '第${chapter.number}章：${chapter.title}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 20),
                // 菜单项
                ListTile(
                  leading: const Icon(Icons.edit, color: Colors.blue),
                  title: const Text('重命名章节'),
                  onTap: () {
                    Get.back();
                    _showRenameChapterDialog(index);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.content_copy, color: Colors.green),
                  title: const Text('复制章节'),
                  onTap: () {
                    Get.back();
                    _duplicateChapter(index);
                  },
                ),
                if (_currentNovel.chapters.length > 1)
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text('删除章节'),
                    onTap: () {
                      Get.back();
                      _deleteChapter(index);
                    },
                  ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      );
    } else {
      // 桌面端使用简单的对话框菜单
      Get.dialog(
        AlertDialog(
          title: Text('第${chapter.number}章：${chapter.title}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('重命名章节'),
                onTap: () {
                  Get.back();
                  _showRenameChapterDialog(index);
                },
              ),
              ListTile(
                leading: const Icon(Icons.content_copy),
                title: const Text('复制章节'),
                onTap: () {
                  Get.back();
                  _duplicateChapter(index);
                },
              ),
              if (_currentNovel.chapters.length > 1)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title:
                      const Text('删除章节', style: TextStyle(color: Colors.red)),
                  onTap: () {
                    Get.back();
                    _deleteChapter(index);
                  },
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('取消'),
            ),
          ],
        ),
      );
    }
  }

  /// 显示重命名章节对话框
  void _showRenameChapterDialog(int index) {
    final chapter = _currentNovel.chapters[index];
    Get.dialog(
      _RenameChapterDialog(
        chapter: chapter,
        onRename: (newTitle) => _renameChapter(index, newTitle),
      ),
    );
  }

  /// 重命名章节
  void _renameChapter(int index, String newTitle) {
    try {
      // 更新章节标题
      _currentNovel.chapters[index] =
          _currentNovel.chapters[index].copyWith(title: newTitle);

      // 更新标题控制器
      if (index < _chapterTitleControllers.length) {
        _chapterTitleControllers[index].text = newTitle;
      }

      setState(() {
        _hasChanges = true;
      });

      Get.snackbar(
        '重命名成功',
        '章节标题已更新为"$newTitle"',
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green[700],
      );
    } catch (e) {
      Get.snackbar(
        '重命名失败',
        '重命名章节时出错: $e',
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red[700],
      );
    }
  }

  /// 复制章节
  void _duplicateChapter(int index) {
    try {
      final originalChapter = _currentNovel.chapters[index];

      // 计算新章节号（跳过第0章）
      final normalChapters = _currentNovel.chapters.where((c) => c.number > 0).toList();
      final newChapterNumber = normalChapters.isEmpty
          ? 1
          : normalChapters
                  .map((c) => c.number)
                  .reduce((a, b) => a > b ? a : b) +
              1;

      // 创建复制的章节
      final duplicatedChapter = Chapter(
        number: newChapterNumber,
        title: '${originalChapter.title}（副本）',
        content: originalChapter.content,
      );

      // 添加到小说中
      _currentNovel.chapters.add(duplicatedChapter);

      // 创建新的控制器
      final newContentController =
          TextEditingController(text: duplicatedChapter.content);
      final newTitleController =
          TextEditingController(text: duplicatedChapter.title);

      // 添加监听器
      newContentController.addListener(_onTextChanged);
      newTitleController.addListener(_onTitleChanged);

      // 创建markdown编辑器
      final newMarkdownEditor = MarkdownEditor(
        controller: newContentController,
        selectable: true,
      );

      // 添加到控制器列表
      _chapterControllers.add(newContentController);
      _chapterTitleControllers.add(newTitleController);
      _markdownEditors.add(newMarkdownEditor);

      setState(() {
        _hasChanges = true;
      });

      // 更新 Agent 控制器
      _agentController.setCurrentNovel(_currentNovel);

      Get.snackbar(
        '复制成功',
        '章节"${duplicatedChapter.title}"已创建',
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green[700],
        icon: const Icon(Icons.check_circle, color: Colors.green),
      );
    } catch (e) {
      Get.snackbar(
        '复制失败',
        '复制章节时出错: $e',
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red[700],
      );
    }
  }

  /// 删除章节
  void _deleteChapter(int index) {
    if (_currentNovel.chapters.length <= 1) {
      Get.snackbar(
        '无法删除',
        '至少需要保留一个章节',
        backgroundColor: Colors.orange.withValues(alpha: 0.1),
        colorText: Colors.orange[700],
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text(
            '确定要删除第${_currentNovel.chapters[index].number}章"${_currentNovel.chapters[index].title}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              _performDeleteChapter(index);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 执行删除章节
  void _performDeleteChapter(int index) {
    try {
      // 确保索引有效
      if (index < 0 || index >= _currentNovel.chapters.length) {
        throw Exception('无效的章节索引: $index');
      }

      // 保存要删除的章节信息用于显示
      final deletedChapterTitle = _currentNovel.chapters[index].title;

      // 先调整当前章节索引，避免访问已删除的控制器
      int newCurrentIndex = _currentChapterIndex;
      if (_currentChapterIndex == index) {
        // 如果删除的是当前章节，切换到前一个或后一个章节
        if (index > 0) {
          newCurrentIndex = index - 1;
        } else if (_currentNovel.chapters.length > 1) {
          newCurrentIndex = 0; // 删除第一章时，切换到新的第一章
        }
      } else if (_currentChapterIndex > index) {
        // 如果删除的章节在当前章节之前，调整索引
        newCurrentIndex--;
      }

      // 安全地移除对应的控制器
      if (index < _chapterControllers.length) {
        final contentController = _chapterControllers.removeAt(index);
        contentController.removeListener(_onTextChanged);
        contentController.dispose();
      }

      if (index < _chapterTitleControllers.length) {
        final titleController = _chapterTitleControllers.removeAt(index);
        titleController.removeListener(_onTitleChanged);
        titleController.dispose();
      }

      // 移除对应的markdown编辑器
      if (index < _markdownEditors.length) {
        _markdownEditors.removeAt(index);
      }

      // 移除章节
      _currentNovel.chapters.removeAt(index);

      // 确保当前章节索引有效
      if (newCurrentIndex >= _currentNovel.chapters.length) {
        newCurrentIndex = _currentNovel.chapters.length - 1;
      }
      if (newCurrentIndex < 0) {
        newCurrentIndex = 0;
      }

      // 更新状态
      setState(() {
        _currentChapterIndex = newCurrentIndex;
        _hasChanges = true;
      });

      // 确保控制器状态一致
      _ensureControllerConsistency();

      // 更新 Agent 控制器
      _agentController.setCurrentNovel(_currentNovel);
      if (_currentNovel.chapters.isNotEmpty &&
          _currentChapterIndex < _currentNovel.chapters.length) {
        _agentController.currentChapter.value =
            _currentNovel.chapters[_currentChapterIndex];
      }

      Get.snackbar(
        '删除成功',
        '章节"$deletedChapterTitle"已删除',
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green[700],
      );
    } catch (e) {
      print('删除章节错误: $e');
      Get.snackbar(
        '删除失败',
        '删除章节时出错: $e',
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red[700],
      );
    }
  }

  /// 构建Markdown编辑器，支持AI修改功能
  Widget _buildMarkdownEditor() {
    if (_currentChapterIndex >= _markdownEditors.length) {
      return Container(
        decoration: const BoxDecoration(
          border: Border.fromBorderSide(BorderSide(color: Colors.grey)),
        ),
        padding: EdgeInsets.all(_isMobile ? 12 : 16),
        child: const Center(
          child: Text(
            '编辑器加载中...',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    final markdownEditor = _markdownEditors[_currentChapterIndex];

    return Column(
      children: [
        Expanded(
          child: markdownEditor.inPlace(),
        ),
        const SizedBox(height: 8),
        markdownEditor.icons,
      ],
    );
  }

  /// 构建可编辑的文本字段，支持AI修改功能（保留作为备用）
  Widget _buildEditableTextField() {
    final controller = _chapterControllers[_currentChapterIndex];

    return TextField(
      controller: controller,
      maxLines: null,
      expands: true,
      textAlignVertical: TextAlignVertical.top,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: '在此编辑章节内容...',
        contentPadding: EdgeInsets.all(_isMobile ? 12 : 16),
      ),
      style: TextStyle(
        fontSize: _isMobile ? 14 : 16,
        height: 1.6,
      ),
      contextMenuBuilder: (context, editableTextState) {
        final selection = editableTextState.textEditingValue.selection;
        final selectedText = selection.isValid && !selection.isCollapsed
            ? controller.text.substring(selection.start, selection.end)
            : '';

        // 如果有选中文本，添加AI修改选项
        if (selectedText.isNotEmpty && selectedText.length <= 500) {
          final aiEditButton = ContextMenuButtonItem(
            label: 'AI修改',
            onPressed: () {
              ContextMenuController.removeAny();
              _showAIEditDialog(selectedText, selection);
            },
          );

          return AdaptiveTextSelectionToolbar.buttonItems(
            anchors: editableTextState.contextMenuAnchors,
            buttonItems: [
              ...editableTextState.contextMenuButtonItems,
              aiEditButton
            ],
          );
        }

        return AdaptiveTextSelectionToolbar.editableText(
          editableTextState: editableTextState,
        );
      },
    );
  }

  /// 显示AI编辑对话框
  Future<void> _showAIEditDialog(
      String selectedText, TextSelection selection) async {
    final promptController = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI修改指令'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('已选中文本：',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                constraints: const BoxConstraints(maxHeight: 100),
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                margin: const EdgeInsets.symmetric(vertical: 8),
                padding: const EdgeInsets.all(8),
                child: SingleChildScrollView(
                  child:
                      Text(selectedText, style: const TextStyle(fontSize: 12)),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: promptController,
                decoration: const InputDecoration(
                  labelText: '修改指令',
                  hintText: '例如：改写成更生动的描述、修正语法错误、增加细节描写等',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                autofocus: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(promptController.text),
            child: const Text('开始修改'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      _modifyTextWithAI(selectedText, result, selection);
    }
  }

  /// 使用AI修改文本
  Future<void> _modifyTextWithAI(
      String originalText, String prompt, TextSelection selection) async {
    try {
      final modifiedTextController = TextEditingController();

      // 显示生成进度对话框
      Get.dialog(
        AlertDialog(
          title: const Text('AI正在修改文本'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const LinearProgressIndicator(),
                const SizedBox(height: 16),
                TextField(
                  controller: modifiedTextController,
                  maxLines: null,
                  readOnly: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.all(8),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('取消'),
            ),
          ],
        ),
        barrierDismissible: false,
      );

      final systemPrompt = '''请根据用户的要求修改以下文本。要求：
1. 保持修改后的文本与上下文的连贯性
2. 保持人物、场景等设定的一致性
3. 确保修改后的文本符合整体风格
4. 避免出现与原意相违背的内容

原文：
$originalText

用户要求：
$prompt''';

      const userPrompt = '请按照上述要求修改文本，直接输出修改后的内容，不需要其他解释。';

      String modifiedText = '';
      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        maxTokens: 2000,
        temperature: 0.7,
      )) {
        modifiedText += chunk;
        modifiedTextController.text = modifiedText;
      }

      Get.back(); // 关闭生成对话框

      // 更新文本内容
      _updateTextContent(selection, modifiedText.trim());

      Get.snackbar(
        '修改完成',
        '文本已更新',
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.back(); // 关闭生成对话框
      Get.snackbar(
        '修改失败',
        e.toString(),
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 更新文本内容
  void _updateTextContent(TextSelection selection, String newText) {
    final controller = _chapterControllers[_currentChapterIndex];
    final originalText = controller.text;

    if (selection.isValid &&
        selection.start >= 0 &&
        selection.end <= originalText.length) {
      final newContent =
          originalText.replaceRange(selection.start, selection.end, newText);
      controller.text = newContent;

      // 标记有修改
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  void dispose() {
    // 取消同步定时器
    _syncTimer?.cancel();

    // 先移除监听器再释放控制器
    for (final controller in _chapterControllers) {
      controller.removeListener(_onTextChanged);
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.removeListener(_onTitleChanged);
      controller.dispose();
    }
    _editorScrollController.dispose();
    super.dispose();
  }

  /// 构建Markdown渲染视图
  Widget _buildMarkdownView() {
    final content = _currentChapterIndex < _chapterControllers.length
        ? _chapterControllers[_currentChapterIndex].text
        : '';

    if (content.isEmpty) {
      return Container(
        decoration: const BoxDecoration(
          border: Border.fromBorderSide(BorderSide(color: Colors.grey)),
        ),
        padding: EdgeInsets.all(_isMobile ? 12 : 16),
        child: const Center(
          child: Text(
            '暂无内容可渲染',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        border: Border.fromBorderSide(BorderSide(color: Colors.grey)),
      ),
      child: Markdown(
        data: content,
        selectable: true,
        padding: EdgeInsets.all(_isMobile ? 12 : 16),
        styleSheet: MarkdownStyleSheet(
          p: TextStyle(
            fontSize: _isMobile ? 14 : 16,
            height: 1.8,
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w400,
          ),
          h1: TextStyle(
            fontSize: _isMobile ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          h2: TextStyle(
            fontSize: _isMobile ? 18 : 22,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          h3: TextStyle(
            fontSize: _isMobile ? 16 : 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          blockquote: TextStyle(
            fontSize: _isMobile ? 14 : 16,
            fontStyle: FontStyle.italic,
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
          strong: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          em: TextStyle(
            fontStyle: FontStyle.italic,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          code: TextStyle(
            fontFamily: 'monospace',
            fontSize: (_isMobile ? 14 : 16) * 0.9,
            backgroundColor:
                Theme.of(context).colorScheme.surfaceContainerHighest,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          codeblockDecoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }
}

/// 创建章节对话框
class _CreateChapterDialog extends StatefulWidget {
  final bool isMobile;
  final Function(String title, String content) onCreateChapter;

  const _CreateChapterDialog({
    required this.isMobile,
    required this.onCreateChapter,
  });

  @override
  State<_CreateChapterDialog> createState() => _CreateChapterDialogState();
}

class _CreateChapterDialogState extends State<_CreateChapterDialog> {
  late final TextEditingController _titleController;
  late final TextEditingController _contentController;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _contentController = TextEditingController();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _handleCreate() {
    final title = _titleController.text.trim();
    if (title.isEmpty) {
      Get.snackbar(
        '提示',
        '请输入章节标题',
        backgroundColor: Colors.orange.withValues(alpha: 0.1),
        colorText: Colors.orange[700],
      );
      return;
    }

    final content = _contentController.text.trim();
    widget.onCreateChapter(title, content);
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.add_circle_outline,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text('创建新章节'),
        ],
      ),
      content: SizedBox(
        width: widget.isMobile ? double.maxFinite : 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '章节标题',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _titleController,
              decoration: InputDecoration(
                hintText: '请输入章节标题',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            const Text(
              '章节内容（可选）',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _contentController,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: '请输入章节内容，也可以稍后编辑',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
        ElevatedButton.icon(
          onPressed: _handleCreate,
          icon: const Icon(Icons.add),
          label: const Text('创建'),
        ),
      ],
    );
  }
}

/// 重命名章节对话框
class _RenameChapterDialog extends StatefulWidget {
  final Chapter chapter;
  final Function(String newTitle) onRename;

  const _RenameChapterDialog({
    required this.chapter,
    required this.onRename,
  });

  @override
  State<_RenameChapterDialog> createState() => _RenameChapterDialogState();
}

class _RenameChapterDialogState extends State<_RenameChapterDialog> {
  late final TextEditingController _titleController;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.chapter.title);
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  void _handleRename() {
    final newTitle = _titleController.text.trim();
    if (newTitle.isNotEmpty && newTitle != widget.chapter.title) {
      widget.onRename(newTitle);
    }
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('重命名第${widget.chapter.number}章'),
      content: TextField(
        controller: _titleController,
        decoration: const InputDecoration(
          labelText: '章节标题',
          border: OutlineInputBorder(),
        ),
        autofocus: true,
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _handleRename,
          child: const Text('确定'),
        ),
      ],
    );
  }
}
