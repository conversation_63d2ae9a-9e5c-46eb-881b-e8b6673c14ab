import 'package:flutter/material.dart';

/// Markdown编辑区域组件
/// 
/// 提供一个可以用于编辑markdown文本的输入框
/// 当指定了 [controller] 时，其 [TextEditingController.text] 定义了初始值
/// 当文本改变时会调用 [onChange] 函数
/// [maxLines] 参数用作 [TextField] 的 [maxLines] 参数
class MarkdownEditingField extends StatelessWidget {
  /// 创建一个 [MarkdownEditingField]
  /// 
  /// [controller] 控制正在编辑的文本，不能为null
  /// [onChange] 文本改变时的回调函数
  /// [maxLines] 最大行数，null表示无限制
  /// [decoration] 输入框装饰
  /// [style] 文本样式
  const MarkdownEditingField({
    Key? key,
    required this.controller,
    this.onChange,
    this.maxLines,
    this.decoration,
    this.style,
  }) : super(key: key);

  /// 控制正在编辑的文本，不能为null
  final TextEditingController controller;

  /// 文本改变时的回调函数
  final VoidCallback? onChange;

  /// 最大行数，null表示无限制
  final int? maxLines;

  /// 输入框装饰
  final InputDecoration? decoration;

  /// 文本样式
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      maxLines: maxLines,
      style: style ?? const TextStyle(
        fontSize: 16,
        height: 1.5,
        fontFamily: 'monospace',
      ),
      decoration: decoration ?? const InputDecoration(
        hintText: '在这里输入Markdown文本...',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.all(16),
      ),
      onChanged: (text) {
        if (onChange != null) {
          onChange!();
        }
      },
    );
  }
}
