import 'package:flutter/material.dart';

typedef PromptFunction = Future<String?> Function();

/// 用于提示输入URL或图片源的函数类型
typedef UrlSource = Future<String?> Function({
  required BuildContext context,
  required String hint,
  required String label,
});

/// Markdown编辑器工具栏
/// 
/// 提供一行图标按钮，可以用来操作 [controller] 中的文本
/// [afterEditing] 函数会在文本编辑后被调用
/// [urlSource] 是一个用于提示输入URL或图片源的函数
class MarkdownEditorIcons extends StatelessWidget {
  /// 创建一个 [MarkdownEditorIcons]
  /// 
  /// [controller] 要操作的文本控制器
  /// [afterEditing] 编辑文本后的回调函数
  /// [urlSource] 用于提示输入URL或图片源的自定义函数
  MarkdownEditorIcons({
    Key? key,
    required this.controller,
    this.afterEditing,
    this.urlSource,
  }) : super(key: key) {
    controller.selection = _lastValidSelection;
    controller.addListener(() {
      if (controller.selection.isValid) {
        _lastValidSelection = controller.selection;
      }
    });
  }

  final ScrollController _scrollController = ScrollController();
  TextSelection _lastValidSelection = const TextSelection.collapsed(offset: 0);

  /// 要操作的文本控制器
  final TextEditingController controller;

  /// 编辑文本后的回调函数
  final VoidCallback? afterEditing;

  /// 用于提示输入URL或图片源的自定义函数
  final UrlSource? urlSource;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Scrollbar(
        controller: _scrollController,
        thumbVisibility: true,
        child: ListView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          children: [
            _buildIconButton(
              icon: Icons.format_bold,
              tooltip: '粗体',
              onPressed: () => _surroundTextSelection('**', '**'),
            ),
            _buildIconButton(
              icon: Icons.format_italic,
              tooltip: '斜体',
              onPressed: () => _surroundTextSelection('*', '*'),
            ),
            _buildIconButton(
              icon: Icons.code,
              tooltip: '代码',
              onPressed: () => _surroundTextSelection('`', '`'),
            ),
            _buildIconButton(
              icon: Icons.strikethrough_s,
              tooltip: '删除线',
              onPressed: () => _surroundTextSelection('~~', '~~'),
            ),
            const VerticalDivider(),
            _buildIconButton(
              icon: Icons.title,
              tooltip: '标题',
              onPressed: () => _addPrefix('# '),
            ),
            _buildIconButton(
              icon: Icons.format_list_bulleted,
              tooltip: '无序列表',
              onPressed: () => _addPrefix('- '),
            ),
            _buildIconButton(
              icon: Icons.format_list_numbered,
              tooltip: '有序列表',
              onPressed: () => _addPrefix('1. '),
            ),
            _buildIconButton(
              icon: Icons.format_quote,
              tooltip: '引用',
              onPressed: () => _addPrefix('> '),
            ),
            const VerticalDivider(),
            _buildIconButton(
              icon: Icons.link,
              tooltip: '链接',
              onPressed: () => _insertLink(context),
            ),
            _buildIconButton(
              icon: Icons.image,
              tooltip: '图片',
              onPressed: () => _insertImage(context),
            ),
            _buildIconButton(
              icon: Icons.table_chart,
              tooltip: '表格',
              onPressed: () => _insertTable(),
            ),
            _buildIconButton(
              icon: Icons.horizontal_rule,
              tooltip: '分割线',
              onPressed: () => _insertText('\n---\n'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return IconButton(
      icon: Icon(icon),
      tooltip: tooltip,
      onPressed: onPressed,
    );
  }



  void _addPrefix(String prefix) {
    final currentText = controller.text;
    final selection = _lastValidSelection;
    final lines = currentText.split('\n');
    final cursorLine = currentText.substring(0, selection.start).split('\n').length - 1;

    if (cursorLine < lines.length) {
      lines[cursorLine] = prefix + lines[cursorLine];
      final newText = lines.join('\n');
      final newOffset = selection.start + prefix.length;

      controller.value = controller.value.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: newOffset),
      );
    }

    _afterEditing();
  }

  void _insertText(String text) {
    final currentText = controller.text;
    final selection = _lastValidSelection;
    final textBefore = selection.textBefore(currentText);
    final textAfter = selection.textAfter(currentText);

    final newText = '$textBefore$text$textAfter';
    final newOffset = selection.start + text.length;

    controller.value = controller.value.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newOffset),
    );

    _afterEditing();
  }

  void _insertLink(BuildContext context) {
    _surroundTextSelection(
      '[',
      '](',
      prompt: () => _getUrlInput(
        context: context,
        hint: 'https://example.com',
        label: '链接地址',
      ),
      afterPrompt: ')',
    );
  }

  void _insertImage(BuildContext context) {
    _surroundTextSelection(
      '![',
      '](',
      prompt: () => _getUrlInput(
        context: context,
        hint: 'https://example.com/image.jpg',
        label: '图片地址',
      ),
      afterPrompt: ')',
    );
  }

  void _insertTable() {
    const table = '''
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |
''';
    _insertText(table);
  }

  void _surroundTextSelection(
    String left,
    String right, {
    PromptFunction? prompt,
    String? afterPrompt,
  }) async {
    final currentText = controller.text;
    final selection = _lastValidSelection;
    final selectedText = selection.textInside(currentText);
    final textBefore = selection.textBefore(currentText);
    final textAfter = selection.textAfter(currentText);

    String insertion = '$left$selectedText$right';
    
    if (prompt != null) {
      final promptResult = await prompt();
      if (promptResult != null) {
        insertion += promptResult;
        if (afterPrompt != null) {
          insertion += afterPrompt;
        }
      }
    }

    final newText = '$textBefore$insertion$textAfter';
    final newOffset = selection.baseOffset + left.length + selectedText.length;

    controller.value = controller.value.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newOffset),
    );

    _afterEditing();
  }

  Future<String?> _getUrlInput({
    required BuildContext context,
    required String hint,
    required String label,
  }) {
    if (urlSource != null) {
      return urlSource!(context: context, hint: hint, label: label);
    }
    return _showInputDialog(context: context, hint: hint, label: label);
  }

  Future<String?> _showInputDialog({
    required BuildContext context,
    required String hint,
    required String label,
  }) {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('输入$label'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _afterEditing() {
    if (afterEditing != null) {
      afterEditing!();
    }
  }
}
